import type { Sparepart } from '$lib/schema/general';
import { retrieve_fetch } from '$lib/utils/fetch';
import { Effect } from 'effect';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ params }) => {
	const opnameId = params.id;

	const getSparepart = retrieve_fetch<Sparepart[]>('/sparepart');
	const response = await Effect.runPromise(getSparepart);

	return { opnameId, list: response.data };
};
