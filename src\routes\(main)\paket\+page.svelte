<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';

	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';
	import DetailModal from './components/DetailModal.svelte';

	const { data } = $props();

	import { getDetailState, setDetailState } from './components/detailState.svelte';
	import DeletePaket from './components/DeletePaket.svelte';
	import type { Jasa, Sparepart } from '$lib/schema/general';

	setDetailState();
	const detailState = getDetailState();
</script>

<div class="flex justify-around gap-2">
	<button
		class="btn btn-primary btn-sm btn-outline"
		onclick={() => {
			detailState.addNewBody();
			detailState.mode = 'add';
			detailState.modal?.showModal();
		}}
	>
		<Icon icon="mdi:plus" /> Tambah Paket
	</button>

	<SearchUsingParam placeholder="Search..." />

	<!-- <button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button> -->
</div>

<br />

<Table
	table_header={[
		['numbering', 'No.'],
		['id_paket', 'ID Paket'],
		['nama_paket', 'Nama Paket'],
		['harga', 'Harga', 'currency'],
		['custom', 'Durasi Estimasi'],
		['keterangan', 'Keterangan'],
		['custom', 'Actions']
	]}
	table_data={data.list}
>
	{#snippet custom({ header, body })}
		{#if header === 'Actions'}
			<div class="flex items-center gap-2">
				<div class="tooltip" data-tip="Lihat Detail">
					<button
						class="btn btn-sm btn-outline btn-primary uppercase"
						onclick={() => {
							detailState.mode = 'view';
							detailState.body = body;
							detailState.jasaPaket = body.jasa_paket as unknown as Jasa[];
							detailState.sparepartPaket = body.sparepart_paket as unknown as Sparepart[];
							detailState.modal?.showModal();
						}}
					>
						<Icon icon="mdi:open-in-new" />
					</button>
				</div>

				<div class="tooltip" data-tip="Edit">
					<button
						class="btn btn-sm btn-outline btn-primary uppercase"
						onclick={() => {
							detailState.mode = 'edit';
							detailState.body = body;
							detailState.jasaPaket = (body.jasa_paket as unknown as Jasa[]) ?? [];
							detailState.sparepartPaket = (body.sparepart_paket as unknown as Sparepart[]) ?? [];
							detailState.modal?.showModal();
						}}
					>
						<Icon icon="mdi:pencil" />
					</button>
				</div>

				<DeletePaket id={body.id_paket} />
			</div>
		{:else if header === 'Durasi Estimasi'}
			{body.durasi_estimasi} menit
		{/if}
	{/snippet}
</Table>

<br />

<div class="flex justify-end">
	<PaginationUsingParam total_content={data.total_rows} />
</div>

<DetailModal />
