import "../chunks/event.js";
import "@sveltejs/kit";
import { q } from "../chunks/query.js";
import { Effect } from "effect";
import { r } from "../chunks/index.js";
import "../chunks/shared.js";
import "../chunks/event-state.js";
import "../chunks/form.js";
import "../chunks/false.js";
import "../chunks/paths.js";
const getSparepartCategory = q(async () => {
  const getCategory = r("/kategorisparepart");
  const response = await Effect.runPromise(getCategory);
  return response;
});
for (const [name, fn] of Object.entries({ getSparepartCategory })) {
  fn.__.id = "1h54b20/" + name;
  fn.__.name = name;
}
export {
  getSparepartCategory
};
