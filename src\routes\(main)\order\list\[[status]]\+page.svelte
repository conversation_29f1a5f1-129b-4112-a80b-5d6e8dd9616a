<script lang="ts">
	import { goto } from '$app/navigation';
	import { shortcut } from '$lib/actions/shortcut.svelte';
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';
	import TimeElapsed from './components/TimeElapsed.svelte';
	import { formatJakartaTime } from '$lib/utils/jakarta-time';
	import FetchOrder from './components/FetchOrder.svelte';
	import DeleteOrder from './components/DeleteOrder.svelte';
	import { type Invoice, type Order } from '$lib/schema/order';
	import { calculateDaysBetweenDates } from '$lib/scripts/dates';
	import VoidInvoice from './components/VoidInvoice.svelte';

	const { data } = $props();

	/**
	 * Calculate time difference between two dates and format as "X Jam Y Menit"
	 * @param startDate - Start date string or Date object
	 * @param endDate - End date string or Date object (optional, defaults to current time)
	 * @returns Formatted time difference string
	 */
	function calculateTimeDifference(startDate: string | Date, endDate?: string | Date): string {
		const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
		const end = endDate ? (typeof endDate === 'string' ? new Date(endDate) : endDate) : new Date();

		// Check for invalid dates
		if (isNaN(start.getTime()) || isNaN(end.getTime())) {
			return 'Invalid date';
		}

		// Calculate difference in milliseconds
		const diffMs = end.getTime() - start.getTime();

		// Convert to minutes (ignore seconds)
		const totalMinutes = Math.floor(Math.abs(diffMs) / (1000 * 60));

		// Calculate hours and remaining minutes
		const hours = Math.floor(totalMinutes / 60);
		const minutes = totalMinutes % 60;

		// Format the result
		let result = '';
		if (hours > 0) {
			result += `${hours} Jam`;
		}
		if (minutes > 0) {
			if (result) result += ' ';
			result += `${minutes} Menit`;
		}

		// Handle case where time is less than 1 minute
		if (!result) {
			result = '0 Menit';
		}

		return result;
	}
</script>

<svelte:window
	use:shortcut={[
		{ key: 'escape', callback: () => goto('/order') },
		{ key: 'enter', callback: () => goto('/order/data') }
	]}
/>

<div class="flex justify-around gap-2">
	<a href="/order" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:arrow-left" /> Kembali
		</button>
	</a>

	<SearchUsingParam placeholder="Search..." />

	{#if data.orderStatus === 'Antrian' || data.orderStatus === ''}
		<a href="/order/data" class="text-nowrap">
			<button class="btn btn-primary btn-sm btn-outline">
				<Icon icon="mdi:plus" /> Tambah Order
			</button>
		</a>
	{/if}

	{#if data.orderStatus === 'Dikerjakan' || data.orderStatus === ''}
		<FetchOrder />
	{/if}

	<!-- <button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button> TEMPORARY : EVERY SORT BY ASCENDING IS COMMENTED FOR NOW -->
</div>

<br />

<Table table_header={data.tableHeader} table_data={data.list}>
	{#snippet custom({ header, body })}
		{#if header === 'No. Invoice'}
			<div>{body.nomor_invoice}</div>
		{:else if header === 'Nama Pelanggan'}
			<div>{body.customer?.nama ?? (body as unknown as Invoice).order.customer.nama}</div>
		{:else if header === 'Nomor Order'}
			<a href="/order/data/{body.nomor_order}" class="link link-primary">
				{body.nomor_order}
			</a>
		{:else if header === 'Jam Masuk'}
			{@const time = body.updated_at || body.created_at}
			<div>{time.split('T')[1].replace('Z', '')}</div>
		{:else if header === 'Waktu Masuk'}
			<div class="text-center">
				{(body as unknown as Invoice).order.created_at?.split('T')[1].replace('Z', '')}
			</div>
		{:else if header === 'Waktu Selesai'}
			<div class="text-center">
				{(body as unknown as Invoice).order.updated_at?.split('T')[1].replace('Z', '')}
			</div>
		{:else if header === 'Rentang Waktu'}
			<div class="text-center">
				{calculateTimeDifference(body.created_at, body.updated_at || new Date())}
			</div>
		{:else if header === 'Waktu Berjalan'}
			<div class="flex items-center justify-center">
				<TimeElapsed start={body.updated_at || body.created_at} />
			</div>
		{:else if header === 'Status'}
			{#if body.status === 'Antrian'}
				<div class="badge badge-primary text-[.75em] font-semibold text-white uppercase">
					{body.status}
				</div>
			{:else if body.status === 'Dikerjakan'}
				<div class="badge badge-warning text-[.75em] font-semibold uppercase">{body.status}</div>
			{:else if body.status === 'Selesai'}
				<div class="badge badge-success text-[.75em] font-semibold uppercase">{body.status}</div>
			{:else if body.status === 'Void'}
				<div class="badge badge-soft text-[.75em] font-semibold text-gray-600 uppercase">
					{body.status}
				</div>
			{/if}
		{:else if header === 'Actions'}
			<div class="flex items-center gap-2">
				{#if body.status === 'Antrian'}
					{#if body.nomor_order}
						<DeleteOrder id={body.nomor_order} />
					{/if}
				{:else if body.status === 'Dikerjakan'}
					<button class="btn btn-sm btn-outline uppercase">
						<Icon icon="ri:pencil-fill" />
						edit

						<span>/</span>

						<Icon icon="ri:receipt-fill" />
						buat invoice
					</button>
				{:else if data.orderStatus === 'Selesai' || body.status === 'Selesai'}
					{@const nomor_order =
						'order' in body ? (body as unknown as Invoice).order.nomor_order : body.nomor_order}

					<a href="/order/data/{nomor_order}">
						<button class="btn btn-sm btn-outline uppercase">
							<Icon icon="ri:external-link-line" />
							lihat invoice
						</button>
					</a>

					<VoidInvoice id={(body as unknown as Invoice).nomor_invoice} />
				{/if}
			</div>
		{/if}
	{/snippet}
</Table>

<br />

<PaginationUsingParam total_content={data.total_rows} />
