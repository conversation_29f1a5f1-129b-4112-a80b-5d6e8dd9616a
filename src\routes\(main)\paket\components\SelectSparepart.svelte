<script lang="ts">
	import type { Sparepart } from '$lib/schema/general';
	import { rupiah } from '$lib/inputs/CurrencyState.svelte';
	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import { getDetailState } from './detailState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import { getUtilityModalState } from '$lib/utils/utility/utilityModalState.svelte';
	import Icon from '@iconify/svelte';
	import { setUtilityModalState } from '$lib/utils/utility/utilityModalState.svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();
	const toastState = getToastState();
	const detailState = getDetailState();
</script>

<button class="btn btn-secondary btn-sm w-fit" onclick={() => utilityModalState.modal?.showModal()}>
	<Icon icon="mdi:car-battery" /> Tambah Item Sparepart
</button>

<UtilityModal url="/sparepart">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Sparepart</h2>
	{/snippet}

	{#snippet item({ item }: { item: Sparepart })}
		{@const alreadySelected = detailState.sparepartPaket.find(
			(sparepart) => sparepart.kode_sparepart === item.kode_sparepart
		)}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start {alreadySelected
				? 'btn-disabled'
				: ''}"
			onclick={() => {
				if (alreadySelected) {
					toastState.add({
						message: 'Sparepart sudah dipilih',
						type: 'error'
					});
					return;
				}
				detailState.sparepartPaket.push(item);
				toastState.add({
					message: `Menambahkan ${item.nama_sparepart} pada paket`,
					type: 'success'
				});
			}}
		>
			{item.nama_sparepart} <span class="font-light"> @ {rupiah(item.harga_jual)}</span>
		</button>
	{/snippet}
</UtilityModal>
