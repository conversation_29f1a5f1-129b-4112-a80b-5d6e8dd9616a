<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';
	import CategoryFilter from './components/CategoryFilter.svelte';
	import FormField from '$lib/utils/formField/FormField.svelte';

	const { data } = $props();
</script>

<div class="grid grid-cols-3 gap-2">
	<fieldset class="fieldset">
		<legend class="fieldset-legend text-primary">Nomor Stok Opname</legend>
	</fieldset>

	<fieldset class="fieldset">
		<legend class="fieldset-legend text-primary">Tanggal Stok Opname</legend>
		<input type="text" class="input w-full" />
	</fieldset>

	<fieldset class="fieldset">
		<legend class="fieldset-legend text-primary"><PERSON>a User</legend>
		<input type="text" class="input w-full" />
	</fieldset>
</div>

<div class="divider"></div>

<div class="flex shrink-0 justify-around gap-2">
	<SearchUsingParam placeholder="Search..." />

	<!-- <button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button> -->

	<button class="btn btn-primary btn-sm">
		<Icon icon="mdi:content-save" /> Simpan Stok Opname
	</button>
</div>

<br />

<CategoryFilter />

<br />

<Table
	table_header={[
		['numbering', 'No.'],
		['nama_sparepart', 'Nama Sparepart'],
		['custom', 'Stok Sistem'],
		['custom', 'Stok Fisik'],
		['custom', 'Selisih'],
		['custom', 'Keterangan']
	]}
	table_data={data.list}
>
	{#snippet custom({ header, body })}
		{#if header === 'Stok Sistem'}
			{body.stok_minimum}
		{:else if header === 'Stok Fisik'}
			<input type="number" class="input input-sm w-16" min={0} value={body.stok_minimum} />
		{:else if header === 'Selisih'}
			{body.stok_minimum - 3}
		{:else if header === 'Keterangan'}
			<input type="text" class="input input-sm w-full" placeholder="keterangan perubahan stok" />
		{/if}
	{/snippet}
</Table>

<br />

<!-- TEMPORARY because of Remote Function Experiment -->

<!-- <div class="flex justify-end">
	<PaginationUsingParam total_content={data.total_rows} />
</div> -->
