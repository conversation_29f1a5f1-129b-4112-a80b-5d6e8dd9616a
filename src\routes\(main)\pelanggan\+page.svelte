<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';
	import DeletePelanggan from './components/DeletePelanggan.svelte';

	import { getDetailState, setDetailState } from './components/detailState.svelte';
	import DetailModal from './components/DetailModal.svelte';

	const { data } = $props();

	setDetailState();
	const detailState = getDetailState();
</script>

<DetailModal />

<div class="flex justify-around gap-2">
	<button
		class="btn btn-primary btn-sm btn-outline"
		onclick={() => {
			detailState.addNewBody();
			detailState.mode = 'add';
			detailState.modal?.showModal();
		}}
	>
		<Icon icon="mdi:plus" /> Tambah Pelanggan
	</button>

	<SearchUsingParam placeholder="Search..." />

	<!-- <button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button> -->
</div>

<br />

<Table
	table_header={[
		['numbering', 'No.'],
		['nama', 'Nama Pelanggan'],
		['custom', 'Alamat'],
		['no_telp', 'Nomor Telepon'],
		['tipe_customer', 'Tipe Customer'],
		['custom', 'Actions']
	]}
	table_data={data.list}
>
	{#snippet custom({ header, body })}
		{#if header === 'Actions'}
			<div class="flex items-center gap-2">
				<div class="tooltip" data-tip="Lihat Detail">
					<button
						class="btn btn-sm btn-outline btn-primary uppercase"
						onclick={() => {
							detailState.mode = 'view';
							detailState.body = body;
							detailState.modal?.showModal();
						}}
					>
						<Icon icon="mdi:open-in-new" />
					</button>
				</div>
				<div class="tooltip" data-tip="Edit">
					<button
						class="btn btn-sm btn-outline btn-primary uppercase"
						onclick={() => {
							detailState.mode = 'edit';
							detailState.body = body;
							detailState.modal?.showModal();
						}}
					>
						<Icon icon="mdi:pencil" />
					</button>
				</div>
				<DeletePelanggan id={body.id_customer} />
			</div>
		{:else if header === 'Alamat'}
			<div>{body.alamat.jalan}</div>
		{/if}
	{/snippet}
</Table>

<br />

<div class="flex justify-end">
	<PaginationUsingParam total_content={data.total_rows} />
</div>
