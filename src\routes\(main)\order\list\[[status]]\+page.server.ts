import type { PageServerLoad } from './$types';

import type { OrderStatus } from '$lib/schema/literal';
import type { TableHeader } from '$lib/table/types';
import type { Order } from '$lib/schema/order';

import { Effect } from 'effect';
import { retrieve_fetch } from '$lib/utils/fetch';
import { capitalize } from 'effect/String';

export const load: PageServerLoad = async ({ params, url }) => {
	const urlParams = url.searchParams;
	const limit = urlParams.get('limit') ?? '10';
	const offset = urlParams.get('offset') ?? '0';
	const keyword = urlParams.get('keyword') ?? '';

	const status =
		params.status === undefined ? '' : params.status === 'proses' ? 'dikerjakan' : params.status;
	const orderStatus = capitalize(status as string) as OrderStatus | '';

	const tableHeader: TableHeader<Order, string> = !orderStatus
		? [
				['numbering', 'No.'],
				['custom', 'Nomor Order'],
				['created_at', 'Tanggal Order', 'date'],

				['custom', '<PERSON>a <PERSON>'],
				['custom', 'Status'],
				['custom', 'Actions']
			]
		: orderStatus !== 'Selesai'
			? [
					['numbering', 'No.'],
					['custom', 'Nomor Order'],
					['created_at', 'Tanggal Order', 'date'],

					['custom', 'Jam Masuk'],
					['custom', 'Waktu Berjalan', 'center'],

					['custom', 'Nama Pelanggan'],
					['custom', 'Status'],
					['custom', 'Actions']
				]
			: [
					['numbering', 'No.'],
					['custom', 'No. Invoice'],

					['created_at', 'Tanggal Order', 'date'],

					['custom', 'Waktu Masuk', 'center'],
					['custom', 'Waktu Selesai', 'center'],
					['custom', 'Rentang Waktu', 'center'],

					['custom', 'Nama Pelanggan'],
					['custom', 'Actions']
				];

	let link =
		orderStatus === ''
			? `/order`
			: orderStatus !== 'Selesai'
				? `/order/status/${orderStatus}`
				: `/invoice`;
	link += `?limit=${limit}&offset=${offset}&keyword=${keyword}`;

	const fetchOrderList = retrieve_fetch<Order[]>(link);
	const orderList = await Effect.runPromise(fetchOrderList);

	return { list: orderList.data, total_rows: orderList.total_rows, orderStatus, tableHeader };
};
