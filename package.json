{"name": "mon<PERSON>r", "version": "0.0.1", "devDependencies": {"@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/adapter-node": "^5.2.13", "@sveltejs/kit": "^2.27.0", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "effect": "^3.17.1", "layerchart": "^1.0.11", "lucide-svelte": "^0.525.0", "oxlint": "^1.9.0", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "runed": "^0.31.0", "svelte": "^5.37.2", "svelte-check": "4.2.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^7.0.4", "vite-plugin-devtools-json": "^0.2.0"}, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . "}, "type": "module"}