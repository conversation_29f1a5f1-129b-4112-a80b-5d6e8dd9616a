<script lang="ts">
	import { rupiah } from '$lib/inputs/CurrencyState.svelte';
	import { getInvoiceState, type Service } from '../../serviceOrder/InvoiceState.svelte';

	interface IProps {
		body: Service[];
	}

	const { body }: IProps = $props();
	const harga = $derived(body.reduce((acc, curr) => acc + curr.harga * curr.qty, 0));

	const invoiceState = getInvoiceState();
</script>

<p class="w-20">{rupiah(harga)}</p>

{#if invoiceState.editable}
	&nbsp;
{/if}
