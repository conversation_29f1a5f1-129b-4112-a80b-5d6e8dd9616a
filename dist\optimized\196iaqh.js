import { isActionFailure } from "@sveltejs/kit";
import "../chunks/event.js";
import { f } from "../chunks/form.js";
import { q } from "../chunks/query.js";
import { Effect } from "effect";
import { r, b } from "../chunks/index.js";
import "../chunks/shared.js";
import "../chunks/event-state.js";
import "../chunks/false.js";
import "../chunks/paths.js";
const getWaitingList = q(async () => {
  const getOrder = r("/order/status/Antrian");
  const response = await Effect.runPromise(getOrder);
  return response.data;
});
const workOnOrder = f(async (data) => {
  const order = JSON.parse(data.get("order"));
  const action = b(`/order`, {
    method: "PUT",
    body: JSON.stringify({
      ...order,
      status_order: "Dikerjakan",
      id_bengkel: order.bengkel.id_bengkel,
      id_karyawan: order.karyawan.id_karyawan,
      id_customer: order.customer.id_customer,
      nomor_polisi: order.kendaraan.nomor_polisi
    })
  });
  const response = await Effect.runPromise(action);
  if (isActionFailure(response)) return { success: false };
  return { ...response, success: true };
});
const deleteOrder = f(async (data) => {
  const nomor_order = data.get("id");
  const action = b(`/order/${nomor_order}`, {
    method: "DELETE"
  });
  const response = await Effect.runPromise(action);
  if (isActionFailure(response)) return { success: false };
  return { ...response, success: true };
});
const voidInvoice = f(async (data) => {
  const nomor_invoice = data.get("id");
  const action = b(`/invoice/${nomor_invoice}`, {
    method: "DELETE"
  });
  const response = await Effect.runPromise(action);
  if (isActionFailure(response)) return { success: false };
  return { ...response, success: true };
});
for (const [name, fn] of Object.entries({ deleteOrder, getWaitingList, voidInvoice, workOnOrder })) {
  fn.__.id = "196iaqh/" + name;
  fn.__.name = name;
}
export {
  deleteOrder,
  getWaitingList,
  voidInvoice,
  workOnOrder
};
