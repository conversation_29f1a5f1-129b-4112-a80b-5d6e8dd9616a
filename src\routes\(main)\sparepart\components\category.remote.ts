import { form } from '$app/server';
import { Effect } from 'effect';
import {
	type KategoriSparepart,
	type KategoriSparepartEncoded,
	KategoriSparepartSchema
} from '$lib/schema/general';
import { launch_fetch } from '$lib/utils/fetch';
import { decodeForm } from '$lib/utils/validation';
import { isActionFailure } from '@sveltejs/kit';

export const createCategory = form(async (data) => {
	const kategori_sparepart = JSON.parse(data.get('kategori_sparepart') as string);

	const decoded = decodeForm<KategoriSparepart, KategoriSparepartEncoded>(
		KategoriSparepartSchema,
		kategori_sparepart
	);

	if ('error' in decoded)
		return { success: false, errors: decoded.errors, message: 'Wrong Form Input' };

	const post = launch_fetch('/kategorisparepart', {
		method: 'POST',
		body: JSON.stringify(decoded)
	});

	const response = await Effect.runPromise(post);

	if (isActionFailure(response)) return { success: false, message: 'Failed to create category' };

	return { success: true, message: 'Category created successfully.' };
});
