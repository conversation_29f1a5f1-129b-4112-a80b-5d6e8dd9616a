<script lang="ts">
	import { invalidateAll } from '$app/navigation';
	import Table from '$lib/table/Table.svelte';
	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { formatJakartaTime } from '$lib/utils/jakarta-time';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import { getWaitingList, workOnOrder } from '../remote/order.remote';
	import TimeElapsed from './TimeElapsed.svelte';
	import Icon from '@iconify/svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	const waitingList = $state(getWaitingList());

	let modal = $state<HTMLDialogElement>();

	const confirmState = getConfirmState();
	const toastState = getToastState();

	let selected_nomor_order: string = $state('');
	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirma<PERSON>',
				description: 'A<PERSON>kah <PERSON><PERSON> yakin akan mengerjakan order ini?',
				loader: 'action:order:' + selected_nomor_order
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<button class="btn btn-sm btn-outline btn-primary" onclick={() => modal?.showModal()}>
	<Icon icon="streamline-flex:wrench-hand-solid" />
	Ambil Antrian
</button>

<svelte:boundary>
	{#snippet failed(error, reset)}
		{error}
	{/snippet}

	<dialog bind:this={modal} class="modal">
		<div class="modal-box w-11/12 max-w-5xl">
			<div class="text-primary mb-2 flex items-center justify-between border-b py-2">
				<h3 class="font-semibold">Antrian Order</h3>
				<form method="dialog">
					<button class="btn btn-sm btn-circle btn-ghost">✕</button>
				</form>
			</div>

			{#if $effect.pending()}
				loading...
			{/if}

			<Table
				table_header={[
					['numbering', 'No.'],
					['nomor_order', 'Nomor Order'],
					['created_at', 'Tanggal Order', 'date'],
					['custom', 'Nama Pelanggan'],
					['custom', 'Jam Masuk'],
					['custom', 'Waktu Berjalan'],
					['custom', 'Actions']
				]}
				table_data={await waitingList}
			>
				{#snippet custom({ header, body })}
					{#if header === 'Nama Pelanggan'}
						{body.customer.nama}
					{:else if header === 'Jam Masuk'}
						{formatJakartaTime(body.created_at)}
					{:else if header === 'Waktu Berjalan'}
						<TimeElapsed start={body.created_at} />
					{:else if header === 'Actions'}
						<form
							{...workOnOrder.enhance(async ({ submit }) => {
								await submit();
								waitingList.refresh();

								if (workOnOrder.result?.success) {
									toastState.add({
										message: 'Order Akan Segera Dikerjakan.',
										type: 'success'
									});
									await invalidateAll();
								} else {
									toastState.add({
										message: 'Gagal Mengubah Status Order.',
										type: 'error'
									});
								}

								modal?.close();
							})}
						>
							<input type="hidden" name="order" value={JSON.stringify(body)} />
							<button
								class="btn btn-sm btn-outline btn-primary uppercase"
								type="button"
								onclick={(e) => {
									selected_nomor_order = body.nomor_order ?? '';
									confirmation(e);
								}}
							>
								<ConfirmLoader name="action:order:{body.nomor_order}">
									<Icon icon="mdi:wrench-clock" />
									Kerjakan
								</ConfirmLoader>
							</button>
						</form>
					{/if}
				{/snippet}
			</Table>
		</div>
	</dialog>
</svelte:boundary>
