import { env } from '$env/dynamic/private';
import { Effect, pipe } from 'effect';

import { BackendError, FetchError, JSONError } from '$lib/errors';
import { fail, type ActionFailure } from '@sveltejs/kit';
import { getRequestEvent } from '$app/server';

export const retrieve = <T>(
	fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,
	url: string,
	init: RequestInit = {}
) =>
	pipe(
		Effect.tryPromise({
			try: () => fetch(env.API_HOST + ':' + env.API_PORT + url, init),
			catch: () => {
				return new FetchError();
			}
		}),
		Effect.flatMap((response) => {
			return Effect.tryPromise({
				try: () => response.json(),
				catch: () => {
					return new JSONError();
				}
			});
		}),
		Effect.flatMap((json) => {
			if (json.status < 200 || json.status >= 300) {
				return Effect.fail(new Error(url + ` : ` + json.message));
			} else {
				const data = ('items' in json.data ? json.data.items : json.data) ?? [];
				const total_rows = ('items' in json.data ? json.data.total_rows : json.data.length) ?? 0;

				return Effect.succeed<{ data: T; total_rows: number }>({ data, total_rows });
			}
		})
	);

export const retrieve_fetch = <T>(url: string, init: RequestInit = {}) =>
	pipe(
		Effect.tryPromise({
			try: () => {
				const { fetch } = getRequestEvent();
				return fetch(env.API_HOST + ':' + env.API_PORT + url, init);
			},
			catch: () => {
				return new FetchError();
			}
		}),
		Effect.flatMap((response) => {
			return Effect.tryPromise({
				try: () => response.json(),
				catch: () => {
					return new JSONError();
				}
			});
		}),
		Effect.flatMap((json) => {
			if (json.status < 200 || json.status >= 300) {
				return Effect.fail(new Error(url + ` : ` + json.message));
			} else {
				const data = ('items' in json.data ? json.data.items : json.data) ?? [];
				const total_rows = ('items' in json.data ? json.data.total_rows : json.data.length) ?? 0;

				return Effect.succeed<{ data: T; total_rows: number }>({ data, total_rows });
			}
		})
	);

export const launch = (
	fetch: (input: RequestInfo | URL, init?: RequestInit) => Promise<Response>,
	url: string,
	init: RequestInit = {}
) =>
	pipe(
		Effect.tryPromise({
			try: () => {
				return fetch(env.API_HOST + ':' + env.API_PORT + url, init);
			},
			catch: () => new FetchError()
		}),
		Effect.flatMap((response) =>
			Effect.tryPromise({
				try: () => response.json(),
				catch: () => new JSONError()
			})
		),
		Effect.catchTags({
			FetchError: () => Effect.succeed(fail(500, { message: `${url} : Failed to fetch data` })),
			JSONError: () => Effect.succeed(fail(500, { message: `${url} : Failed to parse JSON` }))
		}),
		Effect.flatMap((json) => {
			return 'status' in json === false || json.status < 200 || json.status >= 300
				? Effect.fail(new BackendError(json.status ?? 500, json.message, json.errors))
				: Effect.succeed({ ...json });
		}),
		Effect.catchTags({
			BackendError: (_BackendError) =>
				Effect.succeed(
					fail(_BackendError.status, {
						message: `${url} : ${_BackendError.message}`,
						errors: _BackendError.errors
					})
				)
		})
	);

export const launch_fetch = (
	url: string,
	init: RequestInit = {}
): Effect.Effect<
	ActionFailure<
		{ message: string; errors: string[] } | { message: string; status: number; data: any }
	>,
	FetchError | JSONError | BackendError
> =>
	pipe(
		Effect.tryPromise({
			try: () => {
				const { fetch } = getRequestEvent();
				return fetch(env.API_HOST + ':' + env.API_PORT + url, init);
			},
			catch: () => new FetchError()
		}),
		Effect.flatMap((response) =>
			Effect.tryPromise({
				try: () => response.json(),
				catch: () => new JSONError()
			})
		),
		Effect.catchTags({
			FetchError: () => Effect.succeed(fail(500, { message: `${url} : Failed to fetch data` })),
			JSONError: () => Effect.succeed(fail(500, { message: `${url} : Failed to parse JSON` }))
		}),
		Effect.flatMap((json) => {
			return 'status' in json === false || json.status < 200 || json.status >= 300
				? Effect.fail(new BackendError(json.status ?? 500, json.message, json.errors))
				: Effect.succeed({ ...json });
		}),
		Effect.catchTags({
			BackendError: (_BackendError) =>
				Effect.succeed(
					fail(_BackendError.status, {
						message: `${url} : ${_BackendError.message}`,
						errors: _BackendError.errors
					})
				)
		})
	);
