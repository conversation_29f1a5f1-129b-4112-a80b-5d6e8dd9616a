<script lang="ts">
	import { time } from 'effect/Console';
	import { onMount, onDestroy } from 'svelte';

	interface IProps {
		start: string;
	}

	const { start }: IProps = $props();

	let now = $state(new Date());
	let interval: number;

	onMount(() => {
		// Update every second
		interval = setInterval(() => {
			now = new Date();
		}, 1000);
	});

	onDestroy(() => {
		if (interval) {
			clearInterval(interval);
		}
	});

	function getTimeElapsed(): [number, number, number, number] | 'Invalid date' {
		// Simple time difference calculation
		const startDate = new Date(start);

		// Check for invalid dates
		if (isNaN(startDate.getTime())) {
			console.error('Invalid start date:', start);
			return 'Invalid date';
		}

		const diffMs = now.getTime() + 7 * 60 * 60 * 1000 - startDate.getTime(); // Add 7 hours to account for time zone difference

		// Debug logging for negative time
		if (diffMs < 0) {
			console.log('Negative time detected:', {
				start,
				startDate: startDate.toISOString(),
				currentDate: now.toISOString(),
				diffMs,
				startDateLocal: startDate.toString(),
				currentDateLocal: now.toString()
			});
		}

		// Convert to total seconds and format with days if needed
		const totalSeconds = Math.floor(Math.abs(diffMs) / 1000);
		const prefix = diffMs < 0 ? '⚠️ ' : '';

		// Calculate days, hours, minutes, seconds
		const days = Math.floor(totalSeconds / (24 * 3600));
		const remainingSeconds = totalSeconds % (24 * 3600);
		const hours = Math.floor(remainingSeconds / 3600);
		const minutes = Math.floor((remainingSeconds % 3600) / 60);
		const seconds = remainingSeconds % 60;

		// Format with days if >= 24 hours, otherwise just hh:mm:ss
		// if (days > 0) {
		// 	const formattedTime = `${days}D ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
		// 	return `${prefix}${formattedTime}`;
		// } else {
		// 	const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
		// 	return `${prefix}${formattedTime}`;
		// }

		return [
			days,
			Number(hours.toString().padStart(2, '0')),
			Number(minutes.toString().padStart(2, '0')),
			Number(seconds.toString().padStart(2, '0'))
		];
	}

	const timeElapsed = $derived(getTimeElapsed());
</script>

{#if timeElapsed !== 'Invalid date'}
	<span class="countdown font-mono">
		{#if timeElapsed[0] > 0}
			<span
				style="--value:{timeElapsed[0]};"
				aria-live="polite"
				aria-label={timeElapsed[0].toString()}>{timeElapsed[0]}</span
			>
			D&Dot;
		{/if}
		<span
			style="--value:{timeElapsed[1]};"
			aria-live="polite"
			aria-label={timeElapsed[1].toString()}>{timeElapsed[1]}</span
		>
		:
		<span
			style="--value:{timeElapsed[2]};"
			aria-live="polite"
			aria-label={timeElapsed[2].toString()}>{timeElapsed[2]}</span
		>
		:
		<span
			style="--value:{timeElapsed[3]};"
			aria-live="polite"
			aria-label={timeElapsed[3].toString()}>{timeElapsed[3]}</span
		>
	</span>
{/if}
