<script lang="ts">
	import { untrack } from 'svelte';
	import Icon from '@iconify/svelte';
	import ServiceOrderTable from './components/ServiceOrderTable.svelte';

	import logoCombined from '$lib/images/logo-combined.webp';
	import {
		getInvoiceState,
		setInvoiceState,
		type Service
	} from './serviceOrder/InvoiceState.svelte.js';

	import InvoiceConclusion from './components/InvoiceConclusion.svelte';
	import InvoiceFooter from './components/InvoiceFooter.svelte';
	import InvoiceEssentials from './components/InvoiceEssentials.svelte';

	const { data, params } = $props();

	setInvoiceState(data.invoice);
	let invoiceState = $state(getInvoiceState());

	$effect(() => {
		invoiceState = getInvoiceState();

		untrack(() => {
			if (params.order_id && invoiceState) {
				if (data.invoice.order.status === 'Antrian' || data.invoice.order.status === 'Selesai')
					invoiceState.editable = false;
				if (data.invoice.order.status === 'Selesai') invoiceState.invoice = data.invoice;
				invoiceState.order = data.invoice.order;
				invoiceState.ppn = data.invoice.order.pajak;

				invoiceState.retainCustomerandVehicle();

				type Services = [Service[], Service[], Service[], Service[]];
				invoiceState.service_order = data.service_order as Services;
			}
		});
	});
</script>

<main class="flex min-h-full flex-col gap-1">
	<div class="flex items-center gap-4">
		<button class="btn btn-primary btn-sm btn-outline w-fit" onclick={() => window.history.back()}>
			<Icon icon="mdi:arrow-left" /> Kembali
		</button>

		<div class="grow"></div>

		<!-- {#if invoiceState?.invoice?.nomor_invoice}
			<button class="btn btn-primary btn-sm btn-outline w-fit">
				<Icon icon="mdi:printer-outline" font-size="1.2rem" /> Print Invoice
			</button>
		{/if} TEMPORARY-->
	</div>

	<div class="divider my-0 mt-1"></div>

	<InvoiceEssentials />

	<section
		class="invoice-background flex flex-col gap-4 border-2 border-gray-100"
		style="background-image: url({logoCombined}), url({logoCombined}), url({logoCombined});"
	>
		<section class="grow overflow-auto">
			<ServiceOrderTable />
		</section>

		<InvoiceConclusion />
	</section>

	<InvoiceFooter />
</main>

<style>
	.invoice-background {
		background-size: 150px;
		background-color: rgba(255, 255, 255, 0.95);
		background-blend-mode: lighten;
		background-position: 0 0;
		background-repeat: repeat;
	}
</style>
