<script lang="ts">
	import { type Spesialisasi } from '$lib/schema/general';

	import { enhance } from '$app/forms';
	import Icon from '@iconify/svelte';

	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getDetailState } from './detailState.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import {
		getValidationErrorState,
		setValidationErrorState
	} from '$lib/utils/validation/ValidationErrorState.svelte';
	import FormField from '$lib/utils/formField/FormField.svelte';
	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';

	const detailState = getDetailState();

	setValidationErrorState();
	const validationErrorState = getValidationErrorState();

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();

	const confirmState = getConfirmState();
	const toastState = getToastState();
	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `Apakah Anda yakin akan ${detailState.mode === 'add' ? 'menambahkan' : 'menyunting'} montir ini?`,
				loader: 'action:montir'
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<dialog bind:this={detailState.modal} class="modal">
	<div class="modal-box text-primary w-11/12" class:bg-base-200={detailState.mode === 'view'}>
		<div class="flex items-center gap-2">
			<figure class="border-dashed p-2">
				<div class="avatar">
					<div class="h-40 w-40 rounded">
						<img src="https://iili.io/3LxSsMN.webp" alt="Operator Profile Pic" />
					</div>
				</div>
			</figure>
			<div class="grow">
				<div class="mb-4 flex items-center justify-between gap-2 border-b border-b-gray-300 pb-2">
					<h3 class="text-lg font-bold">Detail Montir</h3>
					<form method="dialog">
						<button
							class="btn btn-sm btn-circle btn-soft"
							onclick={() => (validationErrorState.errors = {})}>✕</button
						>
					</form>
				</div>

				<form
					action="?/action:montir"
					method="post"
					enctype="multipart/form-data"
					use:enhance={() =>
						formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
							detailState.modal?.close();
						})}
				>
					<FormField
						name="username"
						label="Username"
						type="text"
						bind:mode={detailState.mode}
						bind:value={detailState.body.username}
					></FormField>

					<br />

					<FormField
						name="password"
						label="Password"
						type="password"
						bind:mode={detailState.mode}
						bind:value={detailState.body.password}
					></FormField>

					<div class="divider"></div>

					{#if detailState.mode !== 'add'}
						<FormField
							name="id_montir"
							label="ID Montir"
							type="text"
							bind:mode={detailState.mode}
							bind:value={detailState.body.id_montir}
							validation={false}
						></FormField>

						<br />
					{/if}

					<div class="flex items-center gap-4" class:hidden={detailState.mode === 'view'}>
						{#if detailState.body.spesialisasi}
							<p class="text-sm font-light">
								Spesialisasi : <span class="font-semibold">
									{detailState.body.spesialisasi?.nama}</span
								>
							</p>
						{/if}

						<button
							type="button"
							class="btn btn-soft btn-primary btn-sm {!detailState.body.spesialisasi
								? 'w-full'
								: 'w-fit'}"
							onclick={() => utilityModalState.modal?.showModal()}
						>
							{#if !detailState.body.spesialisasi}
								Tentukan Spesialisasi
							{/if}
							<Icon icon="mdi:book-open-page-variant-outline" />
						</button>
					</div>

					<br />

					<FormField
						name="nama"
						label="Nama Montir"
						type="text"
						bind:mode={detailState.mode}
						bind:value={detailState.body.nama}
					></FormField>

					<br />

					{#if detailState.mode !== 'view'}
						<FormField
							name="foto"
							id="foto"
							label="Foto Montir"
							type="file"
							bind:mode={detailState.mode}
							bind:value={detailState.body.foto}
							class="file-input"
						></FormField>

						<br />
					{/if}

					<FormField
						name="alamat"
						label="Alamat Montir"
						type="text"
						validationName="alamat>jalan"
						bind:mode={detailState.mode}
						bind:value={detailState.body.alamat.jalan}
					></FormField>

					<br />

					<FormField
						name="no_telp"
						label="Nomor Telepon"
						type="text"
						bind:mode={detailState.mode}
						bind:value={detailState.body.no_telp}
					></FormField>

					<br />

					<FormField
						name="keterangan"
						label="Keterangan"
						type="textarea"
						bind:mode={detailState.mode}
						bind:value={detailState.body.keterangan}
					></FormField>

					<div class="divider"></div>

					<input type="hidden" name="montir" value={JSON.stringify(detailState.body)} />
					<input type="hidden" name="mode" value={detailState.mode} />

					<button
						type="button"
						class="btn btn-primary w-full"
						onclick={(e) => confirmation(e)}
						class:hidden={detailState.mode === 'view'}
					>
						<ConfirmLoader name="action:montir">Simpan</ConfirmLoader>
					</button>
				</form>
			</div>
		</div>
	</div>
</dialog>

<!-- Spesialisasi Selection Modal -->
<UtilityModal url="/spesialisasi">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Spesialisasi</h2>
	{/snippet}

	{#snippet item({ item }: { item: Spesialisasi })}
		<button
			class="btn btn-primary btn-sm btn-soft w-full justify-start"
			onclick={() => {
				detailState.body.spesialisasi = item;
				utilityModalState.modal?.close();
			}}
		>
			{item.nama}
		</button>
	{/snippet}
</UtilityModal>
