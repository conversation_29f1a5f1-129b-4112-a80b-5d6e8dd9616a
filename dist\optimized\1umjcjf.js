import { g } from "../chunks/event.js";
import "@sveltejs/kit";
import { q } from "../chunks/query.js";
import "../chunks/shared.js";
import "../chunks/event-state.js";
import "../chunks/form.js";
import "../chunks/false.js";
import "../chunks/paths.js";
const getData = q(async () => {
  return fetchData();
});
function fetchData() {
  const { fetch } = g();
}
for (const [name, fn] of Object.entries({ getData })) {
  fn.__.id = "1umjcjf/" + name;
  fn.__.name = name;
}
export {
  getData
};
