<script lang="ts">
	import { invalidateAll } from '$app/navigation';

	import Icon from '@iconify/svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import { voidInvoice } from '../remote/order.remote';

	interface IProps {
		id: string;
	}

	const { id }: IProps = $props();

	const confirmState = getConfirmState();
	const toastState = getToastState();
	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
				description: '<PERSON><PERSON><PERSON>h Anda yakin akan membatalkan (void) invoice ini?',
				loader: 'void:invoice:' + id
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<form
	{...voidInvoice.enhance(async ({ submit }) => {
		await submit();

		if (voidInvoice.result?.success) {
			toastState.add({
				message: 'Order berhasil dihapus',
				type: 'success'
			});
			await invalidateAll();
		} else {
			toastState.add({
				message: 'Gagal menghapus order',
				type: 'error'
			});
		}
	})}
>
	<input type="hidden" name="id" value={id} />
	<button
		class="btn btn-sm btn-soft btn-error uppercase"
		type="button"
		onclick={(e) => confirmation(e)}
	>
		<ConfirmLoader name="void:invoice:{id}">
			<Icon icon="ri:close-large-fill" />
			void
		</ConfirmLoader>
	</button>
</form>
