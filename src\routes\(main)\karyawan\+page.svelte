<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Icon from '@iconify/svelte';

	const { data } = $props();

	import { getDetailState, setDetailState } from './components/detailState.svelte';
	import DetailModal from './components/DetailModal.svelte';

	setDetailState();
	const detailState = getDetailState();
</script>

<DetailModal />

<div class="flex h-full flex-col gap-4">
	<div class="flex shrink-0 justify-around gap-2">
		<button
			class="btn btn-primary btn-sm btn-outline"
			onclick={() => {
				detailState.addNewBody();
				detailState.mode = 'add';
				detailState.modal?.showModal();
			}}
		>
			<Icon icon="mdi:plus" /> <PERSON><PERSON> Karyawan
		</button>

		<SearchUsingParam placeholder="Search..." />

		<!-- <button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:sort-alphabetical-ascending" />
			Sort By Ascending
		</button> -->
	</div>

	<div class="flex h-full grow gap-4 overflow-auto">
		<div class="flex flex-col gap-4">
			<div class="grid grid-cols-4 gap-4 overflow-auto">
				{#each data.list as karyawan (karyawan.id_karyawan)}
					<div>
						<div class="card lg:card-side bg-base-100 border border-gray-200 shadow-sm">
							<figure class="w-1/5 shrink-0">
								<img src="https://iili.io/3LxSsMN.webp" alt="Album" />
							</figure>

							<div class="card-body p-4">
								<div class="flex items-center justify-between">
									<div class="tooltip" data-tip={karyawan.nama}>
										<h2 class="line-clamp-1 text-base font-bold">{karyawan.nama}</h2>
									</div>

									<div class="dropdown dropdown-right">
										<button tabindex="0" class="btn btn-ghost btn-circle">
											<Icon icon="mdi:dots-horizontal" font-size="1.2em" />
										</button>
										<!-- svelte-ignore a11y_no_noninteractive_tabindex -->
										<ul
											tabindex="0"
											class="dropdown-content menu bg-base-100 rounded-box z-10 w-52 border border-gray-200 p-2 shadow-lg"
										>
											<li>
												<button
													class="btn btn-sm btn-ghost"
													onclick={() => {
														detailState.mode = 'view';
														detailState.body = karyawan;
														detailState.modal?.showModal();
													}}
												>
													<Icon icon="mdi:eye" /> Lihat Data Karyawan
												</button>
											</li>
											<li>
												<button
													class="btn btn-sm btn-ghost"
													onclick={() => {
														detailState.mode = 'edit';
														detailState.body = karyawan;
														detailState.modal?.showModal();
													}}
												>
													<Icon icon="mdi:pencil" /> Edit Data Karyawan
												</button>
											</li>
										</ul>
									</div>
								</div>

								<div class="tooltip" data-tip={karyawan.keterangan}>
									<p class="line-clamp-1">{karyawan.keterangan} &nbsp;</p>
								</div>

								<div class="flex items-center justify-end">
									<div class="badge badge-success rounded-full">
										{karyawan.status}
									</div>
								</div>
							</div>
						</div>
					</div>
				{/each}
			</div>

			<div class="shrink-0">
				<PaginationUsingParam total_content={data.total_rows} />
			</div>
		</div>
	</div>
</div>
