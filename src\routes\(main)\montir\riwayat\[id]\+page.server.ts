import { Effect } from 'effect';

import type { Montir } from '$lib/schema/general';
import type { PageServerLoad } from './$types';

import { retrieve_fetch } from '$lib/utils/fetch';

export const load: PageServerLoad = async ({ params }) => {
	const montirId = params.id;

	const getRiwayat = retrieve_fetch<string[]>(`/montir/riwayat/${montirId}`);
	const riwayat = await Effect.runPromise(getRiwayat);

	const getMontir = retrieve_fetch<Montir>(`/montir/${montirId}`);
	const montir = await Effect.runPromise(getMontir);

	return {
		riwayat: riwayat.data.map((item) => ({ nomor_order: item })),
		total_rows: riwayat.total_rows,
		montir: montir.data,
		montirId
	};
};
