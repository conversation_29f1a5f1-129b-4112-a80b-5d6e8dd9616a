<script generics="T extends Record<string, any>" lang="ts">
	import type { Snippet } from 'svelte';
	import Icon from '@iconify/svelte';

	import { getUtilityModalState } from './utilityModalState.svelte';

	interface IProps {
		title: Snippet;
		item: Snippet<[{ item: T; index: number }]>;
		url: string;
	}

	const { title, item, url }: IProps = $props();

	const utilityModalState = getUtilityModalState();

	$effect(() => {
		utilityModalState.url = `/api/utility${url}`;

		return () => (utilityModalState.url = '');
	});
</script>

{#key url}
	<dialog class="modal" bind:this={utilityModalState.modal}>
		<div class="modal-box">
			<div class="flex items-center justify-between gap-2">
				{@render title()}

				<form method="dialog">
					<button class="btn btn-sm btn-circle btn-ghost" onclick={() => utilityModalState.reset()}>
						✕
					</button>
				</form>
			</div>

			<br />

			<label class="input input-sm mb-2 w-full">
				<Icon icon="majesticons:search-line" />

				<input
					type="search"
					name="keyword"
					id="keyword"
					class="grow"
					placeholder="Search..."
					bind:value={utilityModalState.keyword}
				/>

				{#if utilityModalState.loading}
					<span class="loading loading-spinner loading-xs"></span>
				{/if}
			</label>

			<br />

			{#if utilityModalState}
				{#each utilityModalState.list ?? [] as _item, index}
					{@render item({ item: _item as T, index })}
				{/each}

				<p class="my-2 text-xs font-light">
					Found {utilityModalState.list?.length} data.
					<!-- {#if utilityModalState.list?.length === 0}
						Please try to be more specific.
					{/if} -->
				</p>
			{:else}
				<p class="text-error my-2 text-xs italic">No Data Found.</p>
			{/if}
		</div>
	</dialog>
{/key}
