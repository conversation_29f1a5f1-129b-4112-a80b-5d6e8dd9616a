<script lang="ts">
	import { page } from '$app/state';
	import { mutateQueryParams } from '$lib/scripts/utils';

	interface IProps {
		total_content: number;
	}
	const { total_content }: IProps = $props();

	const limit = $derived(Number(page.url.searchParams.get('limit') ?? 10));
	const offset = $derived(Number(page.url.searchParams.get('offset') ?? 0));

	const max_page = $derived(Math.ceil(total_content / limit));
	const current_page = $derived(Math.ceil(offset / limit) + 1);
</script>

<div class="join">
	<select
		name="limit"
		id="limit"
		class="select select-primary select-sm join-item w-16 rounded-sm"
		onchange={(e) => {
			mutateQueryParams('limit', () => (e.target as HTMLSelectElement)?.value);
		}}
	>
		<option value="10" selected>10</option>
		<option value="20">20</option>
		<option value="50">50</option>
		<option value="100">100</option>
	</select>

	<button
		class="btn btn-sm btn-primary btn-outline join-item !rounded-s"
		onclick={() => {
			mutateQueryParams('offset', (v) => {
				const value = Number(v);

				if (value - limit < 0) return ((max_page - 1) * limit).toString();
				return (value - limit).toString();
			});
		}}
	>
		prev
	</button>

	<button class="btn join-item btn-sm btn-primary btn-outline w-fit !border-x-0 text-center">
		{current_page} <span class="text-xs">of</span>
		{max_page}
	</button>

	<button
		class="btn join-item btn-sm btn-primary btn-outline !rounded-e"
		onclick={() => {
			mutateQueryParams('offset', (v) => {
				const value = Number(v);

				if (Math.ceil(value / limit) + 1 === max_page) return '0';
				return (value + limit).toString();
			});
		}}
	>
		next
	</button>
</div>
