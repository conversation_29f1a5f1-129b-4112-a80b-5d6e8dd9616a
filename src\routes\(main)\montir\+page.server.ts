import type { Actions, PageServerLoad } from './$types';
import { MontirSchema, type Montir, type Spesialisasi } from '$lib/schema/general';

import { Effect, Schema } from 'effect';
import { launch, retrieve } from '$lib/utils/fetch';
import { decodeForm } from '$lib/utils/validation';
import { fail } from '@sveltejs/kit';
import { env } from '$env/dynamic/public';

export const load: PageServerLoad = async ({ fetch }) => {
	const getMontir = retrieve<Montir[]>(fetch, '/montir');
	const getSpesialisasi = retrieve<Spesialisasi[]>(fetch, '/spesialisasi');

	const [montirResponse, spesialisasiResponse] = await Effect.runPromise(
		Effect.all([getMontir, getSpesialisasi], { concurrency: 'unbounded' })
	);

	const statistics = {
		aktif: montirResponse.data.filter((montir) => montir.status === 'Aktif').length,
		non_aktif: montirResponse.data.filter((montir) => montir.status === 'Nonaktif').length,
		tunggu: montirResponse.data.filter((montir) => montir.status === 'Tunggu').length
	};

	return {
		list: montirResponse.data,
		total_rows: montirResponse.total_rows,
		statistics,
		spesialisasi: spesialisasiResponse.data
	};
};

export const actions: Actions = {
	'action:montir': async ({ request, fetch, locals }) => {
		const data = await request.formData();
		const montir = JSON.parse(data.get('montir') as string) as Montir;
		const mode = data.get('mode');

		// Create form schema that excludes certain fields and adds required ones
		const FormMontirSchema = Schema.Struct({
			...MontirSchema.omit('created_at', 'deleted_at', 'updated_at').fields,
			id_spesialisasi: Schema.String
		});

		type FormMontir = Schema.Schema.Type<typeof FormMontirSchema>;
		type FormMontirEncoded = Schema.Schema.Encoded<typeof FormMontirSchema>;

		const decodedMontir = decodeForm<FormMontir, FormMontirEncoded>(FormMontirSchema, {
			...montir,
			id_spesialisasi: montir.spesialisasi?.id_spesialisasi,
			id_bengkel: env.PUBLIC_ID_BENGKEL
		});

		if ('error' in decodedMontir) return fail(400, { errors: decodedMontir.errors });

		const action = launch(fetch, '/montir', {
			method: mode === 'add' ? 'POST' : 'PUT',
			body: JSON.stringify(decodedMontir)
		});

		const response = await Effect.runPromise(action);
		return response;
	},
	'delete:montir': async ({ request, fetch }) => {
		const data = await request.formData();
		const id = data.get('id') as string;

		const action = launch(fetch, `/montir/${id}`, {
			method: 'DELETE'
		});

		const response = await Effect.runPromise(action);
		return response;
	}
};
