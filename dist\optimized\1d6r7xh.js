import "../chunks/event.js";
import { isActionFailure } from "@sveltejs/kit";
import { f } from "../chunks/form.js";
import { Effect } from "effect";
import { g } from "../chunks/general.js";
import { b } from "../chunks/index.js";
import { d } from "../chunks/index4.js";
import "../chunks/shared.js";
import "../chunks/event-state.js";
import "../chunks/false.js";
import "../chunks/paths.js";
import "../chunks/query.js";
const createCategory = f(async (data) => {
  const kategori_sparepart = JSON.parse(data.get("kategori_sparepart"));
  const decoded = d(
    g,
    kategori_sparepart
  );
  if ("error" in decoded)
    return { success: false, errors: decoded.errors, message: "Wrong Form Input" };
  const post = b("/kategorisparepart", {
    method: "POST",
    body: JSON.stringify(decoded)
  });
  const response = await Effect.runPromise(post);
  if (isActionFailure(response)) return { success: false, message: "Failed to create category" };
  return { success: true, message: "Category created successfully." };
});
for (const [name, fn] of Object.entries({ createCategory })) {
  fn.__.id = "1d6r7xh/" + name;
  fn.__.name = name;
}
export {
  createCategory
};
