<script lang="ts">
	import { applyAction, enhance } from '$app/forms';
	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { formEnhancementBasic } from '$lib/utils/index';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import { setValidationErrorState } from '$lib/utils/validation/ValidationErrorState.svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';
	import FormField from '$lib/utils/formField/FormField.svelte';
	import { goto } from '$app/navigation';

	setValidationErrorState();
	const confirmState = getConfirmState();
	const toastState = getToastState();
</script>

<h2 class="text-center text-2xl font-bold">Log In</h2>

<br />

<form
	action="?/login"
	method="post"
	use:enhance={() => {
		return async ({ result }) => {
			await applyAction(result);

			if (result.type === 'success') {
				toastState.add({
					message: 'Login Success',
					type: 'success'
				});
				await goto('/');
			} else
				toastState.add({
					message: 'Login Failed',
					type: 'error'
				});

			confirmState.loader = '';
		};
	}}
>
	<div>
		<label for="username" class="text-sm font-semibold">
			<span>Username</span>
		</label>
		<FormField
			name="username"
			label="Username"
			type="text"
			noLabel
			value=""
			class="input input-primary w-full shadow"
		/>
	</div>

	<div>
		<label for="password" class="text-sm font-semibold">
			<span>Password</span>
		</label>
		<FormField
			name="password"
			label="Password"
			type="password"
			noLabel
			value=""
			class="input input-primary w-full shadow"
		/>
	</div>

	<button
		class="btn btn-primary mt-4 w-full"
		onclick={() => {
			confirmState.loader = 'login';
		}}
	>
		<ConfirmLoader name="login">Log In</ConfirmLoader>
	</button>
</form>
