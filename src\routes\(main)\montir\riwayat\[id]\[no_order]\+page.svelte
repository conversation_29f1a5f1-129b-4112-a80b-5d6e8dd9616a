<script lang="ts">
	import { untrack } from 'svelte';
	import Icon from '@iconify/svelte';
	import {
		getInvoiceState,
		setInvoiceState,
		type Service
	} from '../../../../order/data/[[order_id]]/serviceOrder/InvoiceState.svelte.js';
	import ServiceOrderTable from '../../../../order/data/[[order_id]]/components/ServiceOrderTable.svelte';

	const { data, params } = $props();

	let invoiceState = $state(getInvoiceState());
	setInvoiceState(data.invoice);

	$effect(() => {
		invoiceState = getInvoiceState();

		untrack(() => {
			invoiceState.order = data.invoice.order;
			invoiceState.editable = false;

			type Services = [Service[], Service[], Service[], Service[]];
			invoiceState.service_order = data.serviceOrder as Services;
		});
	});
</script>

<div class="flex justify-start gap-2">
	<a href="/montir/riwayat/{params.id}" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:arrow-left" /> Kembali
		</button>
	</a>
</div>

<br />

<div class="text-primary flex items-center gap-8">
	<div class="flex items-center gap-2">
		<p class="font-semibold">Nomor Order</p>
		<p class="w-4">:</p>
		<p class="text-sm">{data.invoice.order.nomor_order}</p>
	</div>

	<div class="flex items-center gap-2">
		<p class="font-semibold">Tanggal Order</p>
		<p class="w-4">:</p>
		<p class="text-sm">{data.invoice.order.created_at}</p>
	</div>

	<div class="flex items-center gap-2">
		<p class="font-semibold">Nomor Polisi</p>
		<p class="w-4">:</p>
		<p class="text-sm">{data.invoice.order.kendaraan.nomor_polisi}</p>
	</div>

	<div class="flex items-center gap-2">
		<p class="font-semibold">Kendaraan</p>
		<p class="w-4">:</p>
		<p class="text-sm">{data.invoice.order.kendaraan.nama_kendaraan}</p>
	</div>
</div>

<br />

<section class="grow overflow-auto">
	<ServiceOrderTable />
</section>
