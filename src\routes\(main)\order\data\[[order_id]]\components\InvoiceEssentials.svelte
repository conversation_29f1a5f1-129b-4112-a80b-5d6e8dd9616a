<script lang="ts">
	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte';
	import { jenis<PERSON><PERSON><PERSON> } from '$lib/schema/literal.js';
	import CustomerSearch from './CustomerSearch.svelte';
	import FormField from '$lib/utils/formField/FormField.svelte';
	import VehicleSearch from './VehicleSearch.svelte';

	import logoCombined from '$lib/images/logo-combined.webp';
	import { setValidationErrorState } from '$lib/utils/validation/ValidationErrorState.svelte';

	const invoiceState = getInvoiceState();

	setValidationErrorState();
</script>

<section class="h-fit shrink-0 overflow-auto">
	<div class="flex h-fit shrink-0 gap-2 border-b-2 border-b-gray-300">
		{#if invoiceState}
			<aside class="flex w-5/12 shrink-0 grow flex-col gap-2 p-4 text-xs">
				<div class="text-primary flex items-center gap-2">
					<p class="min-w-fit font-semibold {invoiceState.editable ? 'w-2/12' : 'w-3/12'}">
						Pemilik
					</p>
					<p class="w-2">:</p>
					<div class="flex grow items-center gap-2">
						{#if invoiceState.editable}
							<CustomerSearch />
						{/if}
						<div class="grow">
							<FormField
								noLabel
								name="pemilik"
								placeholder="Nama Pemilik"
								type="text"
								class="input-sm"
								bind:value={invoiceState.order.customer.nama}
								validationName="customer>nama"
								disabled={invoiceState.customerMode === 'terdaftar'}
								asText={!invoiceState.editable}
							></FormField>
						</div>
					</div>
				</div>

				<div class="text-primary flex items-center gap-2">
					<p
						class=" min-w-fit shrink-0 font-semibold {invoiceState.editable ? 'w-2/12' : 'w-3/12'}"
					>
						Nomor Polisi
					</p>
					<p class="w-2">:</p>
					<div class="flex grow items-center gap-2">
						{#if invoiceState.editable}
							<VehicleSearch />
						{/if}

						<div class="grow">
							<FormField
								noLabel
								name="nomor_polisi"
								placeholder="Nomor Polisi"
								type="text"
								bind:value={invoiceState.order.nomor_polisi}
								class="input-sm"
								validationName="nomor_polisi"
								disabled={invoiceState.vehicleMode === 'terdaftar'}
								asText={!invoiceState.editable}
							></FormField>
						</div>
					</div>
				</div>

				<div class="text-primary flex items-center gap-2">
					<p
						class=" min-w-fit shrink-0 font-semibold {invoiceState.editable ? 'w-2/12' : 'w-3/12'}"
					>
						Kendaraan
					</p>
					<p class="w-2">:</p>
					<div class="flex grow items-center gap-4">
						<div class:grow={invoiceState.editable}>
							<FormField
								noLabel
								name="kendaraan"
								placeholder="Nama Kendaraan"
								type="text"
								bind:value={invoiceState.order.kendaraan.nama_kendaraan}
								class="input-sm"
								validationName="kendaraan>nama_kendaraan"
								disabled={invoiceState.vehicleMode === 'terdaftar'}
								asText={!invoiceState.editable}
							></FormField>
						</div>

						<div class="text-primary flex items-center gap-2">
							<p class="min-w-fit font-semibold {invoiceState.editable ? 'w-2/12' : 'w-3/12'}">
								CC
							</p>
							<p class="w-2">:</p>
							<div class="grow">
								<FormField
									noLabel
									name="cc_kendaraan"
									placeholder="CC"
									type="number"
									bind:value={invoiceState.order.kendaraan.cc_kendaraan}
									step="50"
									min="0"
									class="input-sm w-20"
									validationName="kendaraan>cc_kendaraan"
									disabled={invoiceState.vehicleMode === 'terdaftar'}
									asText={!invoiceState.editable}
								></FormField>
							</div>
						</div>
					</div>
				</div>

				<div class="text-primary flex items-center gap-2">
					<p class="min-w-fit font-semibold {invoiceState.editable ? 'w-2/12' : 'w-3/12'}">
						Pengantar
					</p>
					<p class="w-2">:</p>
					<div class="grow">
						<FormField
							noLabel
							name="pengantar"
							placeholder="Pengantar"
							type="text"
							class="input-sm"
							bind:value={invoiceState.order.pengantar}
							validationName="pengantar"
							asText={!invoiceState.editable}
						></FormField>
					</div>
				</div>
			</aside>
		{/if}

		<aside
			class="flex w-2/12 shrink flex-col items-center justify-center gap-1 border-x-2 border-x-gray-300 px-4 text-center"
		>
			<img src={logoCombined} alt="Logo Text" class="w-3/4" />
		</aside>

		{#if invoiceState}
			<aside class="w-5/12 shrink-0 grow p-4 text-xs">
				<div class="mb-2 grid grid-cols-2 items-center gap-2">
					<div class="text-primary flex items-center gap-2">
						<p class="me-0.5 w-1/2 font-semibold text-nowrap">Tanggal Order</p>
						<p class="w-2">:</p>
						<div class="grow"><p>{invoiceState.order?.created_at.split('T')[0]}</p></div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-1/2 font-semibold text-nowrap">Jam</p>
						<p class="w-2">:</p>
						<div class="grow">
							<p>{invoiceState.order?.created_at.split('T')[1]?.replace('Z', '')}</p>
						</div>
					</div>
				</div>

				<div class="mb-2 grid grid-cols-2 items-center gap-2">
					<div class="text-primary flex items-center gap-2">
						<p class="me-0.5 w-1/2 font-semibold text-nowrap">Nomor Order</p>
						<p class="w-2">:</p>
						<div class="grow">
							<p class:text-slate-300={!invoiceState.order?.nomor_order}>
								{invoiceState.order?.nomor_order || 'Belum Dibuat'}
							</p>
						</div>
					</div>

					<div class="text-primary flex items-center gap-2">
						<p class="w-1/2 font-semibold text-nowrap">Nomor Invoice</p>
						<p class="w-2">:</p>
						<div class="grow">
							<p class:text-slate-300={!invoiceState.invoice?.nomor_invoice}>
								{invoiceState.invoice?.nomor_invoice || 'Belum Dibuat'}
							</p>
						</div>
					</div>
				</div>

				<div class="text-primary mb-2 flex items-center gap-2">
					<p class="w-3/12 min-w-fit font-semibold">Nomor HP</p>
					<p class="w-2">:</p>
					<div class="grow">
						<FormField
							name="nomor_hp"
							label="Nomor HP"
							type="text"
							bind:value={invoiceState.order.customer.no_telp}
							class="input-sm"
							noLabel
							validationName="customer>no_telp"
							disabled={invoiceState.customerMode === 'terdaftar'}
							asText={!invoiceState.editable}
						></FormField>
					</div>
				</div>

				<div class="text-primary mb-2 flex items-center gap-2">
					<p class="w-3/12 min-w-fit font-semibold">Jenis Layanan</p>
					<p class="w-2">:</p>
					<div class="grow">
						<FormField
							type="select"
							name="jenis_layanan"
							id="jenis_layanan"
							class="select-sm"
							bind:value={invoiceState.order.jenis_layanan}
							validationName="jenis_layanan"
							noLabel
							asText={!invoiceState.editable}
						>
							{#snippet select_options()}
								{#each jenisLayanan as jenis}
									<option value={jenis}>{jenis}</option>
								{/each}
							{/snippet}
						</FormField>
					</div>
				</div>

				<div class="text-primary mb-2 flex items-center gap-2">
					<p class="w-3/12 min-w-fit font-semibold">Alamat</p>
					<p class="w-2">:</p>
					<div class="grow">
						<FormField
							name="alamat"
							label="Alamat"
							type="text"
							bind:value={invoiceState.order.alamat.jalan}
							validationName="alamat>jalan"
							class="input-sm"
							noLabel
							disabled={invoiceState.customerMode === 'terdaftar'}
							asText={!invoiceState.editable}
						></FormField>
					</div>
				</div>
			</aside>
		{/if}
	</div>
</section>
