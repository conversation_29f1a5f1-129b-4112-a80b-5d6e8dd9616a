import { z } from "zod/v4";
import "../chunks/event.js";
import "@sveltejs/kit";
import { f } from "../chunks/form.js";
import { q } from "../chunks/query.js";
import "../chunks/shared.js";
import "../chunks/event-state.js";
import "../chunks/false.js";
import "../chunks/paths.js";
async function getPokemon$1() {
  const response = await fetch("https://pokeapi.co/api/v2/pokemon");
  if (!response.ok) throw Error("💣️ Failed to fetch Pokemon");
  return (await response.json()).results;
}
async function getPokemonDetails$1(pokemonUrl) {
  const response = await fetch(pokemonUrl || "https://pokeapi.co/api/v2/pokemon/1");
  if (!response.ok) throw Error("💣️ Failed to fetch Pokemon details");
  const pokemon2 = await response.json();
  return {
    name: pokemon2.name,
    image: pokemon2.sprites.front_default
  };
}
const api = { getPokemon: getPokemon$1, getPokemonDetails: getPokemonDetails$1 };
let pokemon = await api.getPokemon();
const getPokemon = q(async () => {
  return pokemon;
});
const getPokemonDetails = q(z.string().optional(), async (pokemonUrl) => {
  return api.getPokemonDetails(pokemonUrl);
});
const likePokemon = f(async (data) => {
  const name = data.get("pokemon");
  pokemon[pokemon.findIndex((p) => p.name === name)].favorite = true;
  await new Promise((resolve) => setTimeout(resolve, 2e3));
  await getPokemon().refresh();
});
for (const [name, fn] of Object.entries({ getPokemon, getPokemonDetails, likePokemon })) {
  fn.__.id = "e3uass/" + name;
  fn.__.name = name;
}
export {
  getPokemon,
  getPokemonDetails,
  likePokemon
};
