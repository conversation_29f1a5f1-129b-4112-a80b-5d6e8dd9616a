<script lang="ts">
	import { invalidateAll } from '$app/navigation';

	import Icon from '@iconify/svelte';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';

	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';

	import { deleteOrder } from '../remote/order.remote';

	interface IProps {
		id: string;
	}

	const { id }: IProps = $props();

	const confirmState = getConfirmState();
	const toastState = getToastState();

	const confirmation = (e: Event) => {
		confirmState.asking(
			{
				title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
				description: '<PERSON><PERSON>kah Anda yakin akan menghapus order ini?',
				loader: 'delete:order:' + id
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<form
	{...deleteOrder.enhance(async ({ submit }) => {
		await submit();

		if (deleteOrder.result?.success) {
			toastState.add({
				message: 'Order berhasil dihapus',
				type: 'success'
			});
			await invalidateAll();
		} else {
			toastState.add({
				message: 'Gagal menghapus order',
				type: 'error'
			});
		}
	})}
>
	<input type="hidden" name="id" value={id} />
	<button
		class="btn btn-sm btn-outline btn-error uppercase"
		type="button"
		onclick={(e) => confirmation(e)}
	>
		<ConfirmLoader name="delete:order:{id}">
			<Icon icon="ri:delete-bin-6-fill" />
			batalkan
		</ConfirmLoader>
	</button>
</form>
