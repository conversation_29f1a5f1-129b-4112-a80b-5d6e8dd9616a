<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';

	const { data, params } = $props();
</script>

<div class="flex justify-around gap-2">
	<a href="/montir" class="text-nowrap">
		<button class="btn btn-primary btn-sm btn-outline">
			<Icon icon="mdi:arrow-left" /> Kembali
		</button>
	</a>

	<SearchUsingParam placeholder="Search..." />

	<!-- <button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button> -->
</div>

<br />

<div class="text-primary flex items-center gap-8">
	<div class="flex items-center gap-2">
		<p class="font-semibold"><PERSON><PERSON></p>
		<p class="w-4">:</p>
		<p class="text-sm">{data.montir.nama}</p>
	</div>

	<div class="flex items-center gap-2">
		<p class="font-semibold">ID Montir</p>
		<p class="w-4">:</p>
		<p class="text-sm">{data.montir.id_montir}</p>
	</div>
</div>

<br />

<Table
	table_header={[
		['numbering', 'No.'],
		['custom', 'Nomor Order']
		// ['created_at', 'Tanggal Order', 'date'],
		// ['custom', 'Nama Kendaraan'],
		// ['custom', 'No. Polisi'],
		// ['custom', 'Jenis Service'],
		// ['customer', 'Nama Pemilik'],
		// ['keterangan', 'Keterangan']
	]}
	table_data={data.riwayat}
>
	{#snippet custom({ header, body })}
		{#if header === 'Nomor Order'}
			<a href="/montir/riwayat/{params.id}/{body.nomor_order}" class="link link-primary">
				{body.nomor_order}
			</a>
		{/if}
	{/snippet}
</Table>

<br />

<div class="flex justify-end">
	<PaginationUsingParam total_content={data.total_rows} />
</div>
