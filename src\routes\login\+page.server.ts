import type { CustomerLoginEncoded } from '$lib/schema/general';
import { fail, type Actions } from '@sveltejs/kit';

export const actions: Actions = {
	login: async ({ request }) => {
		const data = await request.formData();
		const username = data.get('username') as string;
		const password = data.get('password') as string;

		if (username === 'admin' && password === 'admin') {
			return { success: true };
		} else return fail(400, { success: false });
	}
};
