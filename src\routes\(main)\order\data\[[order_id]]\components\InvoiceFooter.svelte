<script lang="ts">
	import { formEnhancementWithValidation } from '$lib/utils/index';
	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte.js';
	import { getConfirmState } from '$lib/utils/confirm/ConfirmState.svelte';
	import { getToastState } from '$lib/utils/toast/ToastState.svelte';
	import { getValidationErrorState } from '$lib/utils/validation/ValidationErrorState.svelte.js';
	import { enhance } from '$app/forms';
	import ConfirmLoader from '$lib/utils/confirm/ConfirmLoader.svelte';
	import Icon from '@iconify/svelte';
	import { goto } from '$app/navigation';

	const invoiceState = getInvoiceState();
	const validationErrorState = getValidationErrorState();
	const confirmState = getConfirmState();
	const toastState = getToastState();

	const submitConfirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `Apakah Anda yakin akan membuat order ini?`,
				loader: 'action:order'
			},
			() => confirmState.submitForm(e)
		);
	};

	const editConfirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `Apakah Anda yakin akan menyimpan perubahan ini?`,
				loader: 'edit:order'
			},
			() => confirmState.submitForm(e)
		);
	};

	const createInvoiceConfirmation = (e: Event) => {
		confirmState.asking(
			{
				title: 'Konfirmasi',
				description: `Apakah Anda yakin akan menyelesaikan order ini?`,
				loader: 'create:invoice'
			},
			() => confirmState.submitForm(e)
		);
	};
</script>

<footer class="flex h-1/12 items-center justify-end gap-2 p-4">
	{#if invoiceState && invoiceState.editable && invoiceState.order.status !== 'Dikerjakan'}
		<form
			action="?/submit:order"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
					goto('/order/list/antrian', { replaceState: true });
				})}
		>
			<input type="hidden" name="order" value={JSON.stringify(invoiceState.order)} />
			<input
				type="hidden"
				name="service_order"
				value={JSON.stringify(invoiceState.service_order)}
			/>

			<input type="hidden" name="subtotal" value={invoiceState.subtotal} />
			<input
				type="hidden"
				name="discount_by_percentage"
				value={invoiceState.discountByPercentage}
			/>
			<input type="hidden" name="discount_by_value" value={invoiceState.discountByValue} />
			<input type="hidden" name="ppn" value={invoiceState.ppn} />
			<input type="hidden" name="total" value={invoiceState.total} />

			<button class="btn btn-primary" type="button" onclick={(e) => submitConfirmation(e)}>
				<ConfirmLoader name="submit:order">
					<Icon icon="ri:download-2-fill" /> Simpan
				</ConfirmLoader>
			</button>
		</form>
	{/if}

	{#if invoiceState && invoiceState.order.status === 'Dikerjakan'}
		<form
			action="?/edit:order"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState)}
		>
			<input type="hidden" name="order" value={JSON.stringify(invoiceState.order)} />
			<input
				type="hidden"
				name="service_order"
				value={JSON.stringify(invoiceState.service_order)}
			/>

			<input type="hidden" name="discount_by_value" value={invoiceState.discountByValue} />
			<input type="hidden" name="ppn" value={invoiceState.ppn} />

			<button class="btn btn-primary" type="button" onclick={(e) => editConfirmation(e)}>
				<ConfirmLoader name="edit:order">
					<Icon icon="majesticons:pencil-alt" /> Edit Order
				</ConfirmLoader>
			</button>
		</form>

		<form
			action="?/create:invoice"
			method="post"
			use:enhance={() =>
				formEnhancementWithValidation(validationErrorState, confirmState, toastState, () => {
					goto('/order/list/selesai', { replaceState: true });
				})}
		>
			<input type="hidden" name="order" value={JSON.stringify(invoiceState.order)} />
			<input type="hidden" name="subtotal" value={invoiceState.subtotal} />
			<input type="hidden" name="total" value={invoiceState.total} />
			<input type="hidden" name="metode_pembayaran" value={invoiceState.order.metode_pembayaran} />

			<button class="btn btn-primary" type="button" onclick={(e) => createInvoiceConfirmation(e)}>
				<ConfirmLoader name="create:invoice">
					<Icon icon="mdi:receipt-text-check" /> Selesaikan Order & Buat Invoice
				</ConfirmLoader>
			</button>
		</form>
	{/if}
</footer>
