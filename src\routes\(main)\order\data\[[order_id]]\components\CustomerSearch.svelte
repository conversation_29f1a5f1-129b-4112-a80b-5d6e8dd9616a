<script lang="ts">
	import { _Customer, type Customer } from '$lib/schema/general';
	import UtilityModal from '$lib/utils/utility/UtilityModal.svelte';
	import {
		getUtilityModalState,
		setUtilityModalState
	} from '$lib/utils/utility/utilityModalState.svelte';
	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte';
	import Icon from '@iconify/svelte';

	setUtilityModalState();
	const utilityModalState = getUtilityModalState();
	const invoiceState = getInvoiceState();
</script>

<div class="join">
	<button
		class="btn btn-sm btn-outline btn-primary join-item"
		class:btn-active={invoiceState.customerMode === 'terdaftar'}
		onclick={() => {
			invoiceState.customerMode = 'terdaftar';

			utilityModalState.modal?.showModal();
		}}
	>
		<Icon icon="mdi:account-search" /> Terdaftar
	</button>

	<button
		class="btn btn-sm btn-outline btn-primary join-item"
		class:btn-active={invoiceState.customerMode === 'baru'}
		onclick={() => {
			invoiceState.customerMode = 'baru';
		}}
	>
		<Icon icon="mdi:account-plus" /> Baru
	</button>
</div>

<UtilityModal url="/customer">
	{#snippet title()}
		<h2 class="font-semibold">Pilih Customer</h2>
	{/snippet}

	{#snippet item({ item }: { item: Customer })}
		<button
			class="btn btn-outline mb-1 w-full"
			onclick={() => {
				invoiceState.order.customer = item;
				invoiceState.order.alamat = item.alamat;
				utilityModalState.modal?.close();
			}}
		>
			{item.nama} ({item.id_customer})
		</button>
	{/snippet}
</UtilityModal>
