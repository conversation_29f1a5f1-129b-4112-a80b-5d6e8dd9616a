<script>
	import { metodePembayaran } from '$lib/schema/literal.js';

	import FormField from '$lib/utils/formField/FormField.svelte';
	import Currency from '$lib/inputs/Currency.svelte';

	import { rupiah } from '$lib/inputs/CurrencyState.svelte.js';

	import { getInvoiceState } from '../serviceOrder/InvoiceState.svelte.js';
	import { setValidationErrorState } from '$lib/utils/validation/ValidationErrorState.svelte.js';

	const invoiceState = getInvoiceState();

	setValidationErrorState();
</script>

{#if invoiceState}
	<section class="mb-2 flex justify-end">
		<div class="w-1/3 text-sm">
			<div class="mb-3 border-y-2 border-y-gray-300 py-3">
				<div class="text-primary mb-2 flex items-center gap-2">
					<p class="w-3/12 min-w-fit font-semibold">Subtotal</p>
					<p class="w-2">:</p>
					<p>{rupiah(invoiceState.subtotal)}</p>
				</div>

				<div class="text-primary mb-2 flex items-center gap-2">
					<p class="w-3/12 min-w-fit shrink-0 font-semibold">Diskon</p>
					<p class="w-2">:</p>
					<div class="flex shrink-0 items-center gap-2">
						{#if invoiceState.editable}
							<FormField
								noLabel
								name="diskon_percentage"
								type="number"
								bind:value={invoiceState.discountByPercentage}
								class="input-xs w-16"
							></FormField>
						{:else}
							<p>{invoiceState.discountByPercentage}</p>
						{/if}

						<p class="shrink-0">% <span class="text-xs">atau</span></p>

						{#if invoiceState.editable}
							<Currency
								name="diskon_value"
								id="diskon_value"
								bind:value={invoiceState.discountByValue}
								class="input-xs w-32"
							/>
						{:else}
							<p>{rupiah(invoiceState.discountByValue)}</p>
						{/if}
					</div>
				</div>

				<div class="text-primary flex items-center gap-2">
					<p class="w-3/12 min-w-fit font-semibold">PPN</p>
					<p class="w-2">:</p>
					<div class="flex shrink-0 items-center gap-2">
						{#if invoiceState.editable}
							<FormField
								noLabel
								name="ppn"
								type="number"
								bind:value={invoiceState.ppn}
								class="input-xs w-16"
							></FormField>
						{:else}
							<p>{invoiceState.ppn}</p>
						{/if}
						<p>%</p>

						<p class="text-xs font-light">
							: {rupiah(
								(invoiceState.ppn * (invoiceState.subtotal - invoiceState.discountByValue)) / 100
							)}
						</p>
					</div>
				</div>
			</div>

			<div class="text-primary mb-4 flex items-center gap-2 text-base">
				<p class="w-3/12 min-w-fit font-semibold">Total</p>
				<p class="w-2">:</p>
				<p class="font-bold">{rupiah(invoiceState.total)}</p>
			</div>

			{#if invoiceState.order.status === 'Dikerjakan' || invoiceState.order.status === 'Selesai'}
				{#if invoiceState.editable}
					<FormField
						name="metode_pembayaran"
						label="Metode Pembayaran"
						placeholder="Metode Pembayaran"
						type="select"
						bind:value={invoiceState.order.metode_pembayaran}
						class="input"
					>
						{#snippet select_options()}
							{#each metodePembayaran as metode}
								<option value={metode}>{metode}</option>
							{/each}
						{/snippet}
					</FormField>
				{:else}
					<div class="text-primary mb-2 flex items-center gap-2 text-wrap">
						<p class="w-3/12 min-w-fit font-semibold">Metode Pembayaran</p>
						<p class="w-2">:</p>
						<p>{invoiceState.order.metode_pembayaran}</p>
					</div>
				{/if}
			{/if}
		</div>
	</section>
{/if}
