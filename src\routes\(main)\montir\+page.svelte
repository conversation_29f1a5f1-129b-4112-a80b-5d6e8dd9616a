<script lang="ts">
	import PaginationUsingParam from '$lib/table/Pagination_Using_Param.svelte';
	import SearchUsingParam from '$lib/table/Search_Using_Param.svelte';
	import Table from '$lib/table/Table.svelte';
	import Icon from '@iconify/svelte';
	import DeleteMontir from './components/DeleteMontir.svelte';
	import { getDetailState, setDetailState } from './components/detailState.svelte';
	import DetailModal from './components/DetailModal.svelte';

	const { data } = $props();

	setDetailState();
	const detailState = getDetailState();
</script>

<div class="stats shadow">
	<div class="stat text-success">
		<div class="stat-title">Karyawan Aktif</div>
		<div class="stat-value">{data.statistics.aktif}</div>
	</div>
	<div class="stat text-error">
		<div class="stat-title"><PERSON><PERSON><PERSON> Non Aktif</div>
		<div class="stat-value">{data.statistics.non_aktif}</div>
	</div>
</div>

<div class="divider"></div>

<div class="flex justify-around gap-2">
	<button
		class="btn btn-primary btn-sm btn-outline"
		onclick={() => {
			detailState.addNewBody();
			detailState.mode = 'add';
			detailState.modal?.showModal();
		}}
	>
		<Icon icon="mdi:plus" /> Tambah Montir
	</button>

	<SearchUsingParam placeholder="Search..." />
	<!-- 
	<button class="btn btn-primary btn-sm btn-outline">
		<Icon icon="mdi:sort-alphabetical-ascending" />
		Sort By Ascending
	</button> -->
</div>

<br />

<Table
	table_header={[
		['numbering', 'No.'],
		['nama', 'Nama'],
		['custom', 'Spesialisasi'],
		['no_telp', 'No. Telp'],
		['custom', 'Status'],
		['custom', 'Riwayat'],
		['custom', 'Actions']
	]}
	table_data={data.list}
>
	{#snippet custom({ header, body })}
		{#if header === 'Spesialisasi'}
			{body.spesialisasi?.nama}
		{:else if header === 'Status'}
			{#if body.status === 'Aktif'}
				<div class="badge badge-success text-[.75em] font-semibold text-white uppercase">
					{body.status}
				</div>
			{:else if body.status === 'Nonaktif'}
				<div class="badge badge-error text-[.75em] font-semibold text-white uppercase">
					{body.status}
				</div>
			{:else if body.status === 'Tunggu'}
				<div class="badge badge-warning text-[.75em] font-semibold text-white uppercase">
					{body.status}
				</div>
			{/if}
		{:else if header === 'Riwayat'}
			<div class="flex items-center gap-2">
				<a href="/montir/riwayat/{body.id_montir}" class="text-nowrap">
					<button class="btn btn-sm btn-outline btn-primary uppercase">
						<Icon icon="mdi:history" />
						Riwayat
					</button>
				</a>
			</div>
		{:else if header === 'Actions'}
			<div class="flex items-center gap-2">
				<div class="tooltip" data-tip="Lihat Detail">
					<button
						class="btn btn-sm btn-outline btn-primary uppercase"
						onclick={() => {
							detailState.mode = 'view';
							detailState.body = body;
							detailState.modal?.showModal();
						}}
					>
						<Icon icon="mdi:open-in-new" />
					</button>
				</div>

				<div class="tooltip" data-tip="Edit">
					<button
						class="btn btn-sm btn-outline btn-primary uppercase"
						onclick={() => {
							detailState.mode = 'edit';
							detailState.body = body;
							detailState.modal?.showModal();
						}}
					>
						<Icon icon="mdi:pencil" />
					</button>
				</div>

				<DeleteMontir id={body.id_montir} />
			</div>
		{/if}
	{/snippet}
</Table>

<br />

<div class="flex justify-end">
	<PaginationUsingParam total_content={data.total_rows} />
</div>

<DetailModal />
