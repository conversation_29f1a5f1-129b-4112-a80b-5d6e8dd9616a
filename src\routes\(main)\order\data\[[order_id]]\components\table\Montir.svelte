<script lang="ts">
	import type { Montir, Paket } from '$lib/schema/general';
	import { getInvoiceState, type Service } from '../../serviceOrder/InvoiceState.svelte';

	import MontirModal from './utility/MontirModal.svelte';

	interface IProps {
		body: Service[];
		index: number;
	}

	const { body, index: groupIndex }: IProps = $props();

	const kind = $derived(body[0]?.kind);

	const invoiceState = getInvoiceState();
</script>

{#if !invoiceState.editable}
	&nbsp;
	<div class="mb-1"></div>
{/if}

<div class="flex flex-col gap-1">
	<ol class="flex flex-col">
		{#each body as b, index}
			{@const montir = b.montir as Montir}
			{@const isSelected = !!montir}

			<li class="mb-1 flex items-center justify-center gap-2 border-b border-b-gray-200">
				{#if isSelected}
					<div class="pb-1">&bullet; {montir.nama} ({montir.id_montir})</div>
				{/if}

				{#if invoiceState.editable}
					<MontirModal {groupIndex} {index} {isSelected} />
				{/if}
			</li>
		{/each}
	</ol>

	{#if (kind === 'jasa' || kind === 'sparepart') && invoiceState.editable}
		<p>&nbsp;</p>
	{/if}
</div>
