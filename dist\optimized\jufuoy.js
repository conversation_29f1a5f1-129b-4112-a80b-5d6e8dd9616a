import "../chunks/event.js";
import "@sveltejs/kit";
import { q } from "../chunks/query.js";
import { r } from "../chunks/index.js";
import { Effect } from "effect";
import "../chunks/shared.js";
import "../chunks/event-state.js";
import "../chunks/form.js";
import "../chunks/false.js";
import "../chunks/paths.js";
const retrieveAll = q(async () => {
  const getStokOpname = r("/stock/opname");
  const response = await Effect.runPromise(getStokOpname);
  return response;
});
for (const [name, fn] of Object.entries({ retrieveAll })) {
  fn.__.id = "jufuoy/" + name;
  fn.__.name = name;
}
export {
  retrieveAll
};
