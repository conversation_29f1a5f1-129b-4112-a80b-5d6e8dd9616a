import { Effect } from 'effect';

import type { PageServerLoad } from './$types';

import { retrieve_fetch } from '$lib/utils/fetch';
import {
	_Invoice,
	type Invoice,
	type Order,
	type OrderJasa,
	type OrderPaket,
	type OrderSparepart
} from '$lib/schema/order';
import type { Mutable } from 'effect/Types';
import type { Service } from '../../../../order/data/[[order_id]]/serviceOrder/InvoiceState.svelte';

export const load: PageServerLoad = async ({ params }) => {
	const montirId = params.id;
	const noOrder = params.no_order;

	const getDetailRiwayat = retrieve_fetch<
		Record<
			'order_jasa' | 'order_paket' | 'order_sparepart',
			(OrderJasa | OrderPaket | OrderSparepart)[]
		>
	>(`/montir/riwayat/${montirId}/${noOrder}`);
	const detailRiwayat = await Effect.runPromise(getDetailRiwayat);

	const serviceOrder: Service[][] = [];

	for (const key in detailRiwayat.data) {
		if (Object.prototype.hasOwnProperty.call(detailRiwayat.data, key)) {
			let element = detailRiwayat.data[key as keyof typeof detailRiwayat.data];

			if (key === 'order_jasa')
				element = element.map((item) => ({
					...item,
					kind: 'jasa',
					qty: item.kuantitas,
					data: (item as OrderJasa).jasa,
					harga: (item as OrderJasa).jasa.harga,
					montir: item.montir
				}));
			else if (key === 'order_paket')
				element = element.map((item) => ({
					...item,
					kind: 'paket',
					qty: item.kuantitas,
					data: (item as OrderPaket).paket,
					harga: (item as OrderPaket).paket.harga,
					montir: item.montir
				}));
			else if (key === 'order_sparepart')
				element = element.map((item) => ({
					...item,
					kind: 'sparepart',
					qty: item.kuantitas,
					data: (item as OrderSparepart).sparepart,
					harga: (item as OrderSparepart).sparepart.harga_jual,
					montir: item.montir
				}));

			if (element.length > 0) serviceOrder.push(element as unknown as Service[]);
		}
	}

	const getOrder = retrieve_fetch<Order>(`/order/${noOrder}`);
	const order = await Effect.runPromise(getOrder);

	const invoice: Mutable<Invoice> = _Invoice;
	invoice.order = order.data;

	return {
		serviceOrder,
		invoice
	};
};
